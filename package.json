{"name": "dutchman", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "postbuild": "node scripts/copy-vditor-assets.cjs"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@endlessorigin/select_to_ask": "0.2.9-alpha.2", "@floating-ui/dom": "^1.6.8", "@floating-ui/vue": "^1.1.2", "@iktakahiro/markdown-it-katex": "^4.0.1", "@rollup/plugin-commonjs": "^26.0.1", "@types/node": "^22.14.0", "axios": "^1.7.2", "cos-js-sdk-v5": "^1.8.4", "dom-serializer": "^2.0.0", "domhandler": "^5.0.3", "element-plus": "^2.7.8", "entities": "^6.0.0", "github-markdown-css": "^5.8.1", "he": "^1.2.0", "highlight.js": "^11.11.1", "highlightjs": "^9.16.2", "htmlparser2": "^10.0.0", "katex": "^0.16.21", "less": "^4.2.0", "less-loader": "^12.2.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "marked": "^15.0.7", "mitt": "^3.0.1", "parse5": "^7.2.0", "pinia": "^2.2.0", "pinia-plugin-persist": "^1.0.0", "ts-node": "^10.9.2", "turndown": "^7.2.0", "url-parse": "^1.5.10", "vditor": "^3.10.9", "video.js": "^8.17.1", "vite-plugin-mkcert": "^1.17.7", "vue": "^3.4.35", "vue-router": "^4.4.0"}, "devDependencies": {"@element-plus/theme-chalk": "^2.2.16", "@types/he": "^1.2.3", "@types/node": "^20.11.0", "@vitejs/plugin-vue": "^5.0.5", "@vue/tsconfig": "^0.5.1", "code-inspector-plugin": "^0.20.10", "sass": "^1.77.6", "typescript": "^5.2.2", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^28.0.0", "vite": "^5.3.1", "vue-tsc": "^2.0.21"}}