import type {examType, qMode, qType} from "@/utils/constant";
import { unique } from "element-plus/es/utils";
import type {Ref} from "vue";

export interface APIResponse {
    success: boolean;
    data: any;
    message: string;
}
export interface basePrjInfo {
    prform: number,
    prname: string,
    prtype: number,
    id: number,
}
export interface prjInfo4table extends basePrjInfo {
    oprtime: string,
    condition: string,
}
export interface prjInfo2_4table extends basePrjInfo {
    name: string,
    releaseTime: string,
}
export interface recyclePrjInfo extends basePrjInfo {
    deltime: string,
}
// 后端真的不考虑一下统一命名吗...
export interface characterObj {
    name: string;
    count: number;
    id: string;
}
export interface administratorObj {
    number: number;
    id: number;
    title: string;
}
export interface userObj {
    uniqueCode?: string, // no need
    name?: string, // no need
    password?: string, // no need
    showName?: string,
    expiryDate?: string,
    loginTime?: string,
    isValid?: number, // no need
    isAble?: number, // no need
    roleId: number,
    roleName?: string, // no need
    approvalAuthority: string,
    avatar?: string, // no need
    createTime?: string, // no need
    modifiedTime?: string, // no need
    oid: number
}

export interface imgUrl {
    echoUrl: string;
    commUrl: string;
}
export interface prjInfoType {
    prjType: string,
    prjName: string,
    prjAim: string,
    prjGeneral: string,
    prjTagList: tagType[],
    prjTargetList: tagType[],
    prjCover: imgUrl,
    [key: string]: any; // 添加索引签名
}
export interface videoContentType {
    videoCover: imgUrl,
    videoKey: string,
    lecture: lectureType[],
}
export interface videoSectionType extends videoContentType{
    sectionName: string
}
export interface tagType {
    areaCode: string, // (target)klgCode | (tag)id
    title: string,
    choose?: boolean,
}

export interface simpleSectionInfo {
    sectionId: number,
    sectionName: string,
    sectionNum: number,
}
export interface lectureType {
    beginning: number,
    id: number,
    startTime: string,
    endTime: string,
    caption: string
}
export interface examSectionType{
    id: number,
    examAnswer: string,
    examChoices: string[],
    examExplanation: string,
    examTitle: string,
    examType: examType,
}
export interface textSection {
    sectionNum: number,
    sectionTitle: string,
    prText: string,
    sectionId: number,
    sectionContent:examSectionType[],
}
export interface questionType {
    uniqueCode: string,
    tprjId: number,
    isSelect: number,
    keyword: string,
    questionType: string,     // 是什么 | 为什么
    questionId: number,
    showName: string,
    title: string,
    coverPic: string,
    createTime: string,
}
export interface question {
    qId: number,
    isValid?: boolean,
    relatedText: string,
    qContent: string,
    qType: qType, // 是什么 | 为什么
    qMode: qMode, // 必要 | 参考
    klg: tagType[],
    explanation: string,
}
export interface questionItem {
    questionId: number,
    questionDescription?: string,
    associatedWords?:string,
    keyword: string,
    questionType: string,
    questionNecessity: number,
    questionWeight: number,
    userName: string,
    createTime: string,
    answerNumber: number,
    questionState: string,
    canDelete: boolean,
}
export interface questionDetail {
    stem?: string,
    questioner: string,
    answerer: string,
    answerExplanation: string,
    answerKlgs: klgType[],
    createTime: string,
    answerStatusList: taskTypePlus[],
}

export interface answerTask {
    klgName: string,
    areaCode: string,
    areaTitle: string,
    taskStatus: number,
    handlerName: string,
    feedback: string | null,
}

export interface answerItem {
    answerExplanation: string,
    answerId: number,
    answerStatus: number | null,
    createTime :string, 
    keyword: string,
    klgNumber: number,
    title: string,
    type: number,
    questionType: string,
    taskNumber: number,
    questionDescription?: string,
}

export interface answer {
    answerId: number,
    klgNumer: number,
    questionId: number,
    answerExplanation: string,
    taskNumber: number,
    createTime: string,
    questionType: string,
    keyword: string,
    prjTitle: string,
    prjType: number,
    answerStatus: number,
}

export interface klgType {
    code: string,
    title: string,
    type: number
    choose?: boolean,
}
export interface questionType2sort4exam extends question{
    contentId: number,
}
export interface examineType {
    link: string,
    auditor: string,
    result: boolean,
    opinion: string,
}
export interface taskType {
    klgName: string,
    areaTitle: string,
    areaCode: string,
}

export interface taskTypePlus extends taskType {
    taskStatus: number,
    handlerName: string,
    feedback: string | null,
    oid: number,
    taskStyle: string,
    deleted: boolean,
    klgTitle?: string,
    klgNameEditMode?: boolean, // 任务名称编辑模式状态
}

export interface selectionOption {
    contentId: string,   // 选项序号
    text: string        // 选项文本
    isAnswer?: boolean
}

export interface ProofListItem {
    klgProofBlockId?: number | null
    klgProofBlockOrderNum?: number
    conclusion: string
    klgProofCondList: ProofCondItem[]
    isDel?: boolean  //是否可以被删除
  }
  export interface ProofCondItem {
    klgProofCondId?: number | null
    sort?: number
    refId?: string
    cnt: string
    klgProofCondOrderNum?: number
  }

