<script setup lang="ts">
import { ref, watch, nextTick, computed, onMounted } from 'vue';
import { convertMathFormulas } from '@/utils/latexUtils';

// 定义组件属性
interface Props {
  modelValue: string;
  placeholder?: string;
  maxlength?: number;
  width?: string;
  height?: string;
  previewHint?: string;
  showWordLimit?: boolean;
  disabled?: boolean;
}

// 定义组件事件
interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'focus', event: FocusEvent): void;
  (e: 'blur', event: FocusEvent): void;
  (e: 'change', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入内容，支持数学公式',
  maxlength: 256,
  width: '100%',
  height: '35px',
  previewHint: '点击编辑，支持latex公式',
  showWordLimit: true,
  disabled: false
});

const emits = defineEmits<Emits>();

// 响应式数据
const editMode = ref(true);
const renderedContent = ref('');
const inputRef = ref();

// 计算属性
const inputStyle = computed(() => ({
  width: props.width,
  height: props.height
}));

// 预览容器样式，确保与输入框宽度一致
const previewStyle = computed(() => ({
  width: props.width,
  minHeight: props.height
}));

// 监听值变化，实时渲染LaTeX公式
watch(
  () => props.modelValue,
  (newValue) => {
    renderedContent.value = convertMathFormulas(newValue);
  },
  { immediate: true }
);

// 处理输入框失焦
const handleBlur = (event: FocusEvent) => {
  if (props.modelValue?.trim() && renderedContent.value) {
    editMode.value = false;
  }
  emits('blur', event);
};

// 处理预览区域点击
const handlePreviewClick = () => {
  if (props.disabled) return;

  editMode.value = true;
  nextTick(() => {
    if (inputRef.value) {
      inputRef.value.focus();
    }
  });
};

// 处理输入框聚焦
const handleFocus = (event: FocusEvent) => {
  editMode.value = true;
  emits('focus', event);
};

// 处理值更新
const handleInput = (value: string) => {
  emits('update:modelValue', value);
  emits('change', value);
};

// 组件挂载时初始化显示模式
onMounted(() => {
  // 如果有初始内容且已渲染，则显示预览模式
  if (props.modelValue && props.modelValue.trim() && renderedContent.value) {
    editMode.value = false;
  }
});
</script>

<template>
  <div class="editable-preview">
    <!-- 编辑模式：显示输入框 -->
    <el-input
      v-if="editMode"
      ref="inputRef"
      :model-value="modelValue"
      :placeholder="placeholder"
      :style="inputStyle"
      :maxlength="maxlength"
      :show-word-limit="showWordLimit"
      :disabled="disabled"
      @input="handleInput"
      @blur="handleBlur"
      @focus="handleFocus"
    />

    <!-- 预览模式：显示渲染结果 -->
    <div
      v-else-if="renderedContent"
      class="preview-container"
      :class="{ disabled: disabled }"
      :style="previewStyle"
      @click="handlePreviewClick"
    >
      <div class="preview flex-center" v-html="renderedContent"></div>
      <div class="preview-hint">{{ previewHint }}</div>
    </div>
  </div>
</template>

<style scoped lang="less">
.editable-preview {
  width: 100%;

  /* 项目名称预览模式样式 */
  .preview-container {
    box-sizing: border-box;
    position: relative;
    padding: 0 12px;
    border: 2px dashed #d0d0d0;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    .preview {
      p,
      div {
        display: inline-block;
      }
    }
  }

  .preview-container:hover {
    border-color: var(--color-primary);
    background-color: #f8f9ff;
  }
  .preview-hint {
    position: absolute;
    top: -8px;
    right: 15px;
    background: white;
    color: #909399;
    font-size: 12px;
    padding: 0 4px;
    opacity: 0;
    transition: opacity 0.2s;
  }

  .preview-container:hover .preview-hint {
    opacity: 1;
  }
}
</style>
