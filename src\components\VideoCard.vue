<template>
  <el-card :body-style="{ padding: '0px' }">
    <div class="image-wrapper" @click="clickimage">
      <!-- <img v-image-lazy="coverPic" class="image" v-if="type === 'video'" /> -->
      <!-- TODO: 懒加载好像不太稳定？但我也没搜到这个字段的信息，不知道它对性能的优化程度，先改成笨蛋加载了 -->
      <img :src="coverPic" class="image" v-if="type === 'video'" />
      <img :src="coverPic" class="image" v-else-if="type === 'draft'" />
      <img :src="coverPic" class="image" v-else />
      <template v-if="props.prjForm === ContentType.video">
        <el-icon :size="40" class="big-video-icon" color="rgb(170, 170, 170)">
          <VideoPlay />
        </el-icon>
      </template>
      <template v-else-if="props.prjForm === ContentType.document">
        <el-icon :size="40" class="big-video-icon" color="rgb(170, 170, 170)">
          <DocumentCopy />
        </el-icon>
      </template>
      <template v-else>
        <el-icon :size="40" class="big-video-icon" color="rgb(170, 170, 170)">
          <Share />
        </el-icon>
      </template>

      <div class="layer"></div>
    </div>
    <div class="intro">
      <p class="title keyWords ellipsis-text-inline" v-html="title"></p>
      <p
        class="keyWords description"
        v-html="description.length > 45 ? description.substring(0, 45) + '.....' : description"
      ></p>
      <div class="logo-info">
        <div class="left-num">
          <img style="margin-right: 5px; width: 12px; height: 12px" src="@/assets/doc.svg" alt="" />
          {{ prjQuestionNumbers }}
        </div>
        <div></div>
        <div class="date">{{ createTime }}</div>
      </div>
      <!-- 案例学习+知识讲解 -->
      <div class="btn-group">
        <CmpButton type="primary" class="w130" @click="goDetailPage">进入项目</CmpButton>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import CmpButton from '@/components/CmpButton.vue';
import defaultPng from '@/assets/images/home/<USER>';
import { ContentType, GoodsType, LearnType, BuyStatus } from '@/utils/constant';
import { useRouter } from 'vue-router';
import { processAllLatexEquations } from '@/utils/latexUtils';

// import { de } from 'element-plus/es/locale';
const router = useRouter();

interface VideoItf {
  /**
   * 项目封面
   */
  coverPic: string;
  /**
   * 项目表述
   */
  description: string;

  goodsType?: number;
  /**
   * 问题次数
   */
  prjQuestionNumbers?: number;
  /**
   * 学习次数
   */
  prjStudyFrequence?: number;
  /**
   * 项目标题
   */
  title: string;

  //跳转介绍页需要用到的参数
  // goodsId: number;
  //视频形式还是文稿形式
  prjForm: number;
  userCoverPic?: string;
  userName?: string;
  [property: string]: any;
  createTime: string;
}

interface Props extends VideoItf {
  type?: string;
  prjType?: string;
  uniqueCode?: string;
}
// 几种形式，video，draft ，domain
const props = withDefaults(defineProps<Props>(), {
  // 用于设置默认值
  prjType: 'explanation',
  type: 'video',
  coverPic: defaultPng,
  userCoverPic: defaultPng,
  uniqueCode: ''
});

const goPage = async () => {
  // sessionStorage.setItem('spuId', props.spuId);
  try {
    router.push(`/prjdetail?uniqueCode=${props.uniqueCode}`); // 这里
  } catch (error) {
    console.error('Error setting intro data:', error);
  }
};
// 免费的，已购买的，未购买可试看的跳转详情页
// 付费的不可试看的跳转介绍页
const goDetailPage = async () => {
  try {
    // 跳转介绍页
    goPage();
  } catch (error) {
    console.error('Error setting intro data:', error);
  }
};

const clickimage = () => {
  if (props.prjType === 'test' || props.type === 'domain') goPage();
  else goDetailPage();
};
</script>

<style scoped lang="less">
.w130 {
  width: 100%;
  height: 25px;
  font-size: 12px;
}

.el-card {
  width: 300px;
  max-height: 354px;

  border-width: 1px;
  border-style: solid;
  border-color: rgba(242, 242, 242, 1);
  border-radius: 5px;
  -moz-box-shadow: 0px 5px 5px rgba(170, 170, 170, 0.***************);
  -webkit-box-shadow: 0px 5px 5px rgba(170, 170, 170, 0.***************);
  box-shadow: 0px 5px 5px rgba(170, 170, 170, 0.***************);

  margin-bottom: 10px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  font-family: var(--text-family);

  .vip {
    position: absolute;
    left: 5px;
    top: 5px;
    display: inline-block;
    width: 36px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    background-color: #ffd37a;
    border-radius: 2px;
    font-size: 10px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.996);
    z-index: 1;

    &.free {
      background-color: rgb(18, 170, 156);
    }
  }

  .image-wrapper {
    position: relative;
    width: 300px;
    height: 225px;
    overflow: hidden;
  }

  .layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #797979;
    z-index: 1;
    opacity: 0;
  }

  &:hover {
    .layer {
      opacity: 0.5;
    }

    .video-info,
    .big-video-icon {
      opacity: 1;
    }
  }

  .big-video-icon {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: rgb(170, 170, 170);
    opacity: 0;
    z-index: 10;
  }

  .video-info {
    position: absolute;
    width: 100%;
    padding: 0 10px 4px;
    bottom: 5px;
    color: rgb(254, 254, 254);
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    font-size: var(--fontsize-small-project);
    opacity: 0;
    z-index: 10;

    .video-info-left {
      display: flex;
      align-items: center;
    }

    .play-num {
      margin-right: 20px;
    }

    .el-icon {
      width: var(--fontsize-small-project);
      height: var(--fontsize-small-project);
      display: flex;
      align-items: center;
      margin-right: 5px;
    }
  }

  .intro {
    padding: 10px;
    color: var(--color-black);
    .title {
      font-size: 14px;
      font-weight: 600;
      color: #333333;
      line-height: normal;
      font-feature-settings: 'kern';
    }
    .description {
      font-size: 12px;
      font-weight: 400;
      color: #333333;
      line-height: normal;
      font-feature-settings: 'kern';
      min-height: 30px;
    }
  }

  .image {
    width: 100%;
    height: auto;
    display: block;
  }

  .title {
    font-size: var(--fontsize-middle-project);
    font-family: var(--title-family);
    font-weight: 600;
    height: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 1px;
    margin: 0;
  }

  .abstract {
    font-size: var(--fontsize-small-project);
    font-family: var(--text-family);
    font-weight: 400;
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 1.5;
    min-height: 36px;
  }

  .logo-info {
    display: flex;
    align-items: center;
    height: 20px;
    line-height: 20px;
    margin: 5px 0;
    justify-content: space-between;
    font-size: 12px;
    font-weight: 400;
    color: #666;

    .left-num {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }
  }

  .btn-group {
    display: flex;
    justify-content: space-between;
  }
}
</style>
