<script setup lang="ts">
import ModeTypeSwitcher from './components/modeTypeSwitcher.vue';
import LectureLine from './components/lectureLine.vue';
import questionListTable from './components/questionListTable.vue';
import MulQuestionList from './components/MulQuestionList.vue';
import { computed, inject, onMounted, onUnmounted, ref, watch, nextTick } from 'vue';
import type { videoContentType } from '@/utils/type';
import { getAllQuestionApi } from '@/apis/path/lineWordApis';
import { downloadGenerateQuestion, getGenerateCurrent, getSectionDetailApi } from '@/apis/path/prjdetail';
import type { params2GetSecDetail } from '@/apis/path/prjdetail';
import VideoPreview from './components/videoPreview.vue';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import AnswerDialog from '@/components/AnswerDialog.vue';
import QuestionDialog from '@/components/QuestionDialog.vue';
import { useRenderManager } from '@/composables/useRenderManager';
import { useQuestionIcon } from '@/composables/useQuestionIcon';
import { getQuestionList } from '@/utils/lineWord2V2';
import { throttle } from 'lodash-es';
import { ElMessage } from 'element-plus';
import questionIcon from '@/assets/svgs/question.svg';
import GenerateDialog from "@/components/GenerateDialog.vue"
import { useDialogStore } from '@/stores/dialog';

const props = defineProps({
  curProjectId: {
    type: String,
    required: true
  },
  curSectionId: {
    type: Number,
    required: true
  }
});

// 错误提示函数
const throwErr = throttle((msg: string) => {
  ElMessage.error(msg);
}, 5000);

const dialogStore = useDialogStore()
const isQuesGenerating = ref(false)

const curPrjId = ref();
const curSecId = ref();
const curCntId = ref();
const questionList = ref<
  Array<{
    questionId: string;
    associatedWords: string;
  }>
>([]);
const getDataFlag = computed(() => {
  return curPrjId.value !== -1 && curSecId.value !== -1 && curPrjId.value && curSecId.value;
});
const curHeaderMode = ref<number>(0); // 0：项目内容 || 1：问题列表
const refreshFlag = ref(false);
const questionDrawerRef = ref();
const videoCaptionList = ref();
const content = inject('lineWordContent', ref(''));
const videoSectionData = ref<videoContentType>({
  videoCover: {
    echoUrl: '',
    commUrl: ''
  },
  videoKey: '',
  lecture: []
});

// 引用 MulQuestionList 组件
const mulQuesionListRef = ref();

// 使用问题图标管理 composable
const {
  questionIconVisible,
  questionIconPosition,
  currentSelectedText,
  showQuestionIcon,
  handleQuestionIconClick,
  handleDocumentClick,
  questionIconElement
} = useQuestionIcon({
  onIconClick: (selectedText: string) => {
    // 使用 emitter 派发事件，与项目中的事件监听保持一致
    emitter.emit(Event.SHOW_QUESTION_DRAWER, {
      mode: 0,
      item: {
        content: selectedText,
        chapterId: curSecId.value,
        contentId: curCntId.value
      }
    });
  }
});


// 自定义处理点击问题的函数，覆盖默认行为
const handleClickWord = async (el: HTMLElement) => {
  // 获取问题列表
  const id = el.getAttribute('data-qid');

  if (id) {
    const questionList = await getQuestionList(id);

    if (questionList && questionList.length > 0) {
      // 显示浮窗，无论有几个问题
      if (mulQuesionListRef.value && mulQuesionListRef.value.showCard) {
        // 直接调用组件的方法
        mulQuesionListRef.value.showCard({
          questionList: questionList,
          element: el
        });
      }
    }
  }
};

// 使用Render管理器
const renderContent=ref()
const {
  destroyRenderInstance,
  initializeRender,
  reinitializeRender,
  addQuestion,
  removeQuestion
} = useRenderManager({
  containerSelector: '#underline',
  getContentData: () => convertVideoCaptionListToTextArray(),
  questionList,
  onSelect: (data: any) => {
    // 当选中文本时，显示问号图标而不是直接打开弹窗
    if (data && data.content) {
      showQuestionIcon(data);
    } else {
      console.log('❌ 选中文本为空或无效');
    }
  },

  onFinish: (arg: any) => {
    const content = arg.content;
    renderContent.value = content;
    // console.log("renderContent",renderContent.value);
  },
  onClick: (data: any) => {
    const questionElement = data.target;
    // 调用自定义的处理函数
    if (questionElement && questionElement instanceof HTMLElement) {
      handleClickWord(questionElement);
    }
  },
  enableDebugLog: true
});
// 生成包含完整HTML结构的数据数组 - 保留时间戳信息用于句子级高亮
const convertVideoCaptionListToTextArray = () => {
  if (!videoCaptionList.value || !Array.isArray(videoCaptionList.value)) {
    return [];
  }

  const htmlArray: string[] = [];
  videoCaptionList.value.forEach((paragraphList, paragraphIndex) => {
    if (Array.isArray(paragraphList)) {
      // 构建包含时间戳信息的HTML结构
      let paragraphHtml = `<div class="paragraph-wrapper" data-paragraph="${paragraphIndex}">`;
      paragraphHtml += `<div class="text">`;

      // 为每个句子添加oid、data-start、data-end属性
      paragraphList.forEach((item) => {
        paragraphHtml += `<span oid="${item.oid}" data-start="${item.startTime}" data-end="${item.endTime}">${item.caption}</span>`;
      });

      paragraphHtml += `</div></div>`;

      if (paragraphHtml.trim()) {
        htmlArray.push(paragraphHtml);
      }
    }
  });
  console.log('convertVideoCaptionListToTextArray', htmlArray);
  return htmlArray;
};
// 处理转换header的mode
const handleChangeHeader = (newHeaderMode: number) => {
  curHeaderMode.value = newHeaderMode;
  sessionStorage.setItem('mode', `${curHeaderMode.value}`)
};
// 获取小节详情信息
const getSectionData = () => {
  if (curSecId.value && curPrjId.value) {
    const detailParams = ref<params2GetSecDetail>({
      sectionId: curSecId.value,
      uniqueCode: curPrjId.value
    });
    getSectionDetailApi(detailParams.value).then(async (res) => {
      // @ts-ignore
      if (res.success) {
        questionList.value = [];
        videoSectionData.value.lecture = res.data.list.speechText;
        videoCaptionList.value = res.data.list.speechText;
        videoSectionData.value.videoKey = res.data.list.longVideoUrl;
        videoSectionData.value.videoCover = {
          echoUrl: res.data.list.longImageUrl,
          commUrl: res.data.list.shortImageUrl
        };

        // 等待 DOM 更新
        await nextTick();

        // 先获取问题列表，再初始化渲染器
        await handleQuestionList(curSecId.value, curPrjId.value);
      }
    });
  }
};
// 获取全部问题
const handleQuestionList = async (chapterId: string, uniqueCode: string) => {
  try {
    const res = await getAllQuestionApi(chapterId, uniqueCode);
    if (!res.data) {
      console.warn('No question data received');
      return;
    }

    questionList.value = res.data;

    // 等待 DOM 更新后再初始化渲染器
    await nextTick();
    await initializeRender();
  } catch (error) {
    console.error('Error in handleQuestionList:', error);
  }
};

// 添加问题函数
const addQuestionFn = (data: any) => {
  if (data.questionId && data.associatedWords) {
    // 使用 useRenderManager 的 addQuestion 方法
    addQuestion(data.associatedWords, Number(data.questionId));

    // 更新questionList
    questionList.value.push({
      questionId: data.questionId,
      associatedWords: data.associatedWords
    });
  }
};
// 删除问题函数
const removeQuestionFn = async (questionId: string) => {
  // 找到要删除的问题
  const questionIndex = questionList.value.findIndex((item) => item.questionId === questionId);
  if (questionIndex !== -1) {
    const question = questionList.value[questionIndex];

    // 使用 useRenderManager 的 removeQuestion 方法
    removeQuestion(question.associatedWords, Number(questionId));

    // 更新questionList
    questionList.value.splice(questionIndex, 1);
  }
};

const openGenerateDialog = () => {
  dialogStore.generateDialogVisible = true
  dialogStore.sectionId = curSecId.value
  dialogStore.belongProjectType = 5
}

const quesGenerating = ref(false)
const taskId = ref('')

const downloadFile = (content: any) => {
  // 让Blob根据内容自动推断类型（保留空对象）
  const blob = new Blob([content], {});

  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  // 保留download属性但设为空，强制触发下载而非跳转
  link.download = '';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(link.href);
};

const downloadGenerateQues = async () => {
  try {
    console.log('开始下载，taskId:', taskId.value)
    
    // 获取文件blob响应
    const res: any = await downloadGenerateQuestion(taskId.value)
    console.log('下载响应类型:', typeof res)
    console.log('下载响应:', res)
    console.log('是否为Blob:', res instanceof Blob)

    // 检查响应是否为blob
    if (res instanceof Blob) {
      // 使用默认文件名
      const filename = `生成问题_${taskId.value}.xlsx`
      console.log('文件名:', filename)
      
      // 创建blob URL
      const blobUrl = URL.createObjectURL(res)
      console.log('Blob URL:', blobUrl)
      
      // 创建下载链接
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = filename
      
      // 触发下载
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // 清理blob URL
      URL.revokeObjectURL(blobUrl)
      
      console.log('文件下载成功')
    } else {
      console.error('响应不是blob类型:', typeof res)
      console.error('响应内容:', res)
    }
  } catch (error) {
    console.error('下载失败:', error)
    console.error('错误详情:', error.response || error)
    // 可以添加错误提示UI
  }
}
const errorMessage = ref(false)

let timer = null
watch(
  () => isQuesGenerating.value,
  () => {
    if (isQuesGenerating.value) {
      errorMessage.value = false
      timer = setInterval(async () => {
        const res: any = await getGenerateCurrent(curPrjId.value, curSecId.value)
        if (res.success) {
          if (res.data.status == 1) {
            //生成问题结束之后需要请求新的问题列表
            //通知table组件进行请求
            emitter.emit(Event.GENERATE_SUCCESS)
            //成功之后quesGenerating.value为true，用于下载问题
            quesGenerating.value = true
            isQuesGenerating.value = false
            taskId.value = res.data.taskId
            clearInterval(timer)
          } else if (res.data.status == 2) {
            clearInterval(timer)
            isQuesGenerating.value = false
            quesGenerating.value = false
            errorMessage.value = true
          }
        }
        //每五秒请求一次
      }, 5000)
    }
  }
)

onMounted(async () => {
  emitter.on(Event.ADD_QUESTION, addQuestionFn);
  emitter.on(Event.REMOVE_QUESTION, removeQuestionFn);
  curHeaderMode.value = Number(sessionStorage.getItem('mode'))
  emitter.on(Event.GENERATE, () => isQuesGenerating.value = true)

  //每次进入要向后端发送一个请求，查看是否有问题在生成
  //返回一个是否生成的值，默认false，变成true的时候watch监听
  const res: any = await getGenerateCurrent(curPrjId.value, curSecId.value)
  if (res.success) {
    if (res.data.status == 0) {
      isQuesGenerating.value = true
    } else if (res.data.status == 1) {
      isQuesGenerating.value = false
      quesGenerating.value = true
      taskId.value = res.data.taskId
    } else if (res.data.status == 2) {
      clearInterval(timer)
      isQuesGenerating.value = false
      quesGenerating.value = false
      errorMessage.value = true
    }
  }
});
onUnmounted(() => {
  emitter.off(Event.ADD_QUESTION, addQuestionFn);
  emitter.off(Event.REMOVE_QUESTION, removeQuestionFn);
  sessionStorage.removeItem('mode')
  emitter.off(Event.GENERATE)
  destroyRenderInstance(); // 清理 render 实例
  clearInterval(timer)
});
// 初始化当前secId和prjId
watch(
  () => props,
  async (newValue) => {
    curSecId.value = newValue.curSectionId;
    curPrjId.value = newValue.curProjectId;
    if (getDataFlag.value) {
      getSectionData();
    }
    const res: any = await getGenerateCurrent(curPrjId.value, curSecId.value)
    if (res.success) {
      errorMessage.value = false
      if (res.data.status == 0) {
        isQuesGenerating.value = true
      } else if (res.data.status == 1) {
        isQuesGenerating.value = false
        quesGenerating.value = true
        taskId.value = res.data.taskId
      } else if (res.data.status == 2) {
        clearInterval(timer)
        isQuesGenerating.value = false
        quesGenerating.value = false
        errorMessage.value = true
      }
    }
  },
  { deep: true, immediate: true }
);
// 观察划词内容
watch(
  () => content,
  (newVal) => {
    // 添加空值检查，防止访问undefined的属性
    if (newVal && newVal.value) {
      const query = {
        content: newVal.value,
        chapterId: curSecId.value,
        contentId: curCntId.value
      };
      emitter.emit(Event.SHOW_QUESTION_DRAWER, { mode: 0, item: query });
    }
  },
  { deep: true, immediate: true }
);
// 模式切换相关代码已删除，统一处理交互逻辑

// 监听 videoCaptionList 变化，确保在数据加载后重新初始化渲染器
watch(
  () => videoCaptionList.value,
  async (newVal) => {
    if (newVal && newVal.length > 0) {
      await nextTick();
      await initializeRender();
    }
  },
  { deep: true }
);

// 监听模式切换，确保在切换回讲稿内容时重新初始化划词系统
watch(
  () => curHeaderMode.value,
  async (newMode, oldMode) => {
    if (oldMode === 0 && newMode !== 0) {
      // 从讲稿内容切换到其他模式时，销毁 Render 实例
      destroyRenderInstance();
    } else if (newMode === 0 && oldMode !== 0) {
      // 切换回讲稿内容时，重新初始化 Render 实例
      await nextTick(); // 等待 DOM 更新

      // 确保数据已加载
      if (videoCaptionList.value && videoCaptionList.value.length > 0) {
        await initializeRender();
      }
    }
  }
);
</script>

<template>
  <div class="video-question-wrapper">
    <!-- header -->
    <div class="header-wrapper">
      <div style="margin-bottom: 10px">
        <mode-type-switcher :mode="curHeaderMode" @changeMode="handleChangeHeader" :type="'video'"
          :modeValues="[0, 1, 2]">
          <template v-slot:mode0> 讲稿内容 </template>
          <template v-slot:mode1> 问题列表 </template>
          <template v-slot:mode2> 视频内容 </template>
        </mode-type-switcher>
      </div>
    </div>
    <div class="btn-container">
      <div v-if="!isQuesGenerating">
        <span class="download" v-if="quesGenerating" @click="downloadGenerateQues">下载问题</span>
        <span style="color: red;" v-if="errorMessage">生成问题失败，请重试</span>
        <CmpButton type="primary" class="w130" @click="openGenerateDialog">生成问题</CmpButton>
      </div>
      <div v-else>
        <span style="vertical-align: bottom; margin-right: 5px;">正在处理中...</span>
        <div class="w131">生成问题</div>
      </div>
    </div>
    <!-- content -->
    <div class="main-wrapper">
      <template v-if="curHeaderMode === 0">
        <!-- 视频文稿 -->
        <div class="lecture-container">
          <!--<div class="switch-bar">
            <el-switch v-model="mode" />
            <span class="bar-text">{{ curWordModeText }}</span>
          </div>-->
          <div id="underline" class="lecture-content">
            <lecture-line
              v-for="(lec, idx) in renderContent"
              :key="lec.id || idx"
              :lecture="lec"
              :index="idx"
              :step="3"
              :renderContent="typeof lec === 'string' ? lec : ''"
            ></lecture-line>
          </div>
        </div>
      </template>
      <template v-else-if="curHeaderMode === 1">
        <!-- 问题列表 -->
        <div class="question-container">
          <question-list-table :cur-prj-id="curProjectId" :cur-sec-id="curSectionId"></question-list-table>
        </div>
      </template>
      <!-- 预览 -->
      <template v-else-if="curHeaderMode === 2">
        <div class="preview-wrapper">
          <video-preview style="margin-top: 30px; margin-bottom: 30px" :project-id="curPrjId"
            :cover="videoSectionData.videoCover" :video="videoSectionData.videoKey"></video-preview>
        </div>
      </template>
    </div>
  </div>

  <!-- 问号图标 -->
  <div v-if="questionIconVisible" ref="questionIconElement" class="question-icon" :style="{
    position: 'fixed',
    left: questionIconPosition.x + 'px',
    top: questionIconPosition.y + 'px',
    zIndex: 10000
  }" @click="handleQuestionIconClick">
    <!-- 悬浮提示 -->
    <div class="question-tooltip">提问</div>
    <!-- 问号图标 -->
    <div class="question-icon-circle">
      <img :src="questionIcon" alt="" />
    </div>
  </div>

  <!-- 之所以把浮窗写在这是因为在index里面还没挂载 -->
  <!-- 浮窗列表 -->
  <mul-question-list ref="mulQuesionListRef" id="mul-list"></mul-question-list>
  <AnswerDialog></AnswerDialog>
  <QuestionDialog></QuestionDialog>
  <GenerateDialog></GenerateDialog>
</template>

<style scoped lang="less">
:deep(.el-drawer__header) {
  margin-bottom: 0;
}

:deep(.highlight) {
  color: var(--color-primary);
}

.video-question-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 55px;
  width: 100%;
  position: relative;

  .btn-container {
    position: absolute;
    right: 20px;
    top: 10px;

    .download {
      color: var(--color-primary);
      cursor: pointer;
      vertical-align: bottom;
      margin-right: 5px;
    }

    .download:hover {
      font-weight: 600;
    }

    .w130 {
      width: 120px;
      height: 35px;
      font-size: 14px;
      border-radius: 4px;
    }

    .w131 {
      width: 120px;
      height: 35px;
      border-radius: 2px;
      font-weight: 400;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      cursor: not-allowed;
      background-color: #DCDFE6;
      border: 1px solid #DCDFE6;
      color: white;
    }
  }

  .main-wrapper {
    display: flex;
    flex-direction: row;
    width: 100%;

    .lecture-container {
      width: 100%;
      //background-color: var(--color-light);

      .switch-bar {
        margin-top: 5px;
        margin-left: 10px;
        display: flex;
        align-items: center;

        .bar-text {
          margin-left: 10px;
          font-size: 14px;
          font-weight: bold;
          font-family: var(--text-family);
        }
      }

      .lecture-content {
        width: 100%;
        //height: 440px;
        //overflow: scroll;
        //overflow-x: hidden;
      }
    }

    .question-container {
      display: flex;
      width: 100%;
    }
  }
}
</style>
