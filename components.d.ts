/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AddKlgBlock: typeof import('./src/components/AddKlgBlock.vue')['default']
    AnswerDialog: typeof import('./src/components/AnswerDialog.vue')['default']
    AnswerDrawer: typeof import('./src/components/AnswerDrawer.vue')['default']
    Backtop: typeof import('./src/components/Backtop.vue')['default']
    ClassicCKEditor: typeof import('./src/components/classicCKEditor.vue')['default']
    CmpButton: typeof import('./src/components/CmpButton.vue')['default']
    CodeBlock: typeof import('./src/components/CodeBlock.vue')['default']
    ContentRenderer: typeof import('./src/components/ContentRenderer.vue')['default']
    EditAnsDrawer: typeof import('./src/components/EditAnsDrawer.vue')['default']
    EditDialog: typeof import('./src/components/EditDialog.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSkeletonItem: typeof import('element-plus/es')['ElSkeletonItem']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ExerInfoBlock: typeof import('./src/components/ExerInfoBlock.vue')['default']
    FormSwitch: typeof import('./src/components/FormSwitch.vue')['default']
    GenerateDialog: typeof import('./src/components/GenerateDialog.vue')['default']
    InlineCKEditor: typeof import('./src/components/inlineCKEditor.vue')['default']
    MyButton: typeof import('./src/components/MyButton.vue')['default']
    MyFlipper: typeof import('./src/components/MyFlipper.vue')['default']
    MyTag: typeof import('./src/components/MyTag.vue')['default']
    PrjInfo: typeof import('./src/components/PrjInfo.vue')['default']
    QuestionDialog: typeof import('./src/components/QuestionDialog.vue')['default']
    QuestionDrawer: typeof import('./src/components/QuestionDrawer.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ThumbNail: typeof import('./src/components/ThumbNail.vue')['default']
    Vditor: typeof import('./src/components/editors/Vditor.vue')['default']
    VditorInline: typeof import('./src/components/editors/VditorInline.vue')['default']
    VideoCard: typeof import('./src/components/VideoCard.vue')['default']
  }
}
