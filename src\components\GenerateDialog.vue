<template>
  <el-dialog v-model="generateDialogVisible" width="910" top="90px">
    <template #header>
      <div class="header">
        <span>生成问题</span>
      </div>
    </template>
    <div class="middle">
      <InlineEditor
        v-model="prompt"
        style="width: 100%"
        :height="630"
        :showToolbar="true"
        :placeholder="'请输入提示词'"
      >
      </InlineEditor>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleConfirm" style="width: 120px; height: 35px">
          提交
        </el-button>
        <el-button @click="handleClose" style="width: 120px; height: 35px">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useDialogStore } from '@/stores/dialog';
import { ref, watch } from 'vue';
import InlineEditor from '@/components/editors/VditorInline.vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import { generateQuestion } from '@/apis/path/prjdetail';
import { stringify } from 'querystring';

const dialogStore = useDialogStore();
const generateDialogVisible = ref(dialogStore.generateDialogVisible);

const route = useRoute();
const uniqueCode = route.query.uniqueCode as string;
const sectionId = ref('');
const belongProjectType = ref(0);

watch(
  () => dialogStore.generateDialogVisible,
  (newVal: boolean) => {
    if (newVal) {
      sectionId.value = dialogStore.sectionId;
      belongProjectType.value = dialogStore.belongProjectType;
    }
    generateDialogVisible.value = newVal;
  },
  { immediate: true } // 立即执行一次以初始化值
);

watch(generateDialogVisible, (newVal) => {
  if (newVal !== dialogStore.generateDialogVisible) {
    dialogStore.generateDialogVisible = newVal;
  }
});
const handleClose = () => {
  prompt.value = '';
  generateDialogVisible.value = false;
  dialogStore.sectionId = '';
  dialogStore.belongProjectType = 0;
};

const handleConfirm = async () => {
  const res: any = await generateQuestion(
    uniqueCode,
    belongProjectType.value,
    prompt.value,
    sectionId.value
  );
  if (res.success) {
    emitter.emit(Event.GENERATE);
    generateDialogVisible.value = false;
    dialogStore.sectionId = '';
    dialogStore.belongProjectType = 0;
    ElMessage.success('开始生成问题');
  }
};
const prompt = ref(`<role> 你是一名学生，要针对所阅读的资料提出所有可能的问题 </role>

<instructions>

1. 问题分为两种 是什么和为什么
2. 是什么：针对资料中的所有术语名词提出"是什么"的问题，也就是 xxx 是什么
3. 为什么：针对资料中的所有可以提出"为什么"问题的语句或者片段，提出"为什么"问题，也就是 xxx 为什么
4. 输出结构是一个 json 数组：[{original_content_fragment:"xxx",question_content:"xxx",question_type:"是什么"},{original_content_fragment:"xxx",question_content:"xxx",question_type:"为什么"}]
5. original_content_fragment字段的值，用于将当前问题反标到原文上，所以必须是资料原文中的某段连续的内容片段
6. question_type字段的值用于说明当前问题的类型，可以是“是什么”或者“为什么”
7. question_content字段的值用于说明当前问题：如果是“是什么”类型的问题，则 question_content字段的值需要与 original_content_fragment字段的值保持一致；如果是“为什么”类型的问题，则 question_content字段的值可以与 original_content_fragment字段的值不一致，只要能表达当前的为什么问题的意思即可；
8. 只输出 json 数组，不要其他内容；

</instructions>

<example>

以下是几个例子，分别包含输入的资料和输出的问题。

---

例子 1：

---

输入资料：

---

整个代码环境分为三个主要环节：① 设置参数和配置（路径、库文件、网表文件）；② 将网表文件和库文件 link；③ 设置约束并进行时序分析。

1.设置参数和配置

######env and config

设置 design 的名字（与 netlist 中的网表相同）；

set design_name "netlist_by_hand"

---

输出问题：

[

{original_content_fragment:"参数",question_content:"参数", question_type:"是什么"},

{original_content_fragment:"路径",question_content:"路径",question_type:"是什么"},

{original_content_fragment:"网表",question_content:"网表",question_type:"是什么"},

{original_content_fragment:"设置参数和配置（路径、库文件、网表文件）；",question_content:"为什么要设置路径",question_type:"为什么"},

{original_content_fragment:"设置参数和配置（路径、库文件、网表文件）；",question_content:"为什么要设置库文件",question_type:"为什么"},

{original_content_fragment:"设置参数和配置（路径、库文件、网表文件）；",question_content:"为什么要设置网表文件",question_type:"为什么"},

{original_content_fragment:"将网表文件和库文件 link；",question_content:"为什么要将网表文件和库文件 link",question_type:"为什么"},

{original_content_fragment:"约束",question_content:"约束", question_type:"为什么"},

{original_content_fragment:"时序分析",question_content:"时序分析",question_type:"是什么"},

{original_content_fragment:"设置约束并进行时序分析。", question_content:"为什么要设置约束",question_type:"为什么"},

{original_content_fragment:"design",question_content:"design", question_type:"是什么"},

{original_content_fragment:"design 的名字",question_content:"design 的名字",question_type:"是什么"},

{original_content_fragment:"设置 design 的名字",question_content:"为什么要设置 design 的名字",question_type:"为什么"},

{original_content_fragment:"netlist",question_content:"netlist", question_type:"是什么"},

{original_content_fragment:"set",question_content:"set",question_type:"是什么"},

{original_content_fragment:"design_name",question_content:"design_name",question_type:"是什么"}

]

---

例子 2：

---

输入资料：

SNN 由大量非线性神经元通过突触连接而成，每个神经元的状态由其膜电位、离子通道动力学等变量描述。直接模拟所有神经元的微分方程和脉冲事件，计算成本高昂，且难以理解其整体行为。

---

输出问题：

[

{original_content_fragment:"SNN",question_content:"SNN",question_type:"是什么"},

{original_content_fragment:"非线性神经元", question_content:"非线性神经元",question_type:"是什么"},

{original_content_fragment:"突触",question_content:"突触", question_type:"是什么"},

{original_content_fragment:"神经元的状态", question_content:"神经元的状态",question_type:"是什么"},

{original_content_fragment:"膜电位",question_content:"膜电位",question_type:"是什么"},

{original_content_fragment:"离子通道动力学",question_content:"离子通道动力学",question_type:"是什么"},

{original_content_fragment:"微分方程", question_content:"微分方程", question_type:"是什么"},

{original_content_fragment:"脉冲事件",question_content:"脉冲事件",question_type:"是什么"},

{original_content_fragment:"模拟所有神经元的微分方程和脉冲事件",question_content:"模拟所有神经元的微分方程和脉冲事件",question_type:"是什么"},

{original_content_fragment:"直接模拟所有神经元的微分方程和脉冲事件，计算成本高昂，且难以理解其整体行为。",question_content:"为什么直接模拟所有神经元的微分方程和脉冲事件，计算成本高昂",question_type:"为什么"},

{original_content_fragment:"直接模拟所有神经元的微分方程和脉冲事件，计算成本高昂，且难以理解其整体行为。",question_content:"为什么通过直接模拟所有神经元的微分方程和脉冲事件，难以理解 SNN 的整体行为",question_type:"为什么"},

]

---

</example>

<references></references>`);
</script>

<style scoped lang="less">
.header {
  width: 880px;
  font-size: 16px;
  font-weight: 700;
  color: #333333;
  padding-bottom: 10px;
  border-bottom: 1px solid #dcdfe6;
}

.middle {
  width: 830px;
  height: 630px;
  margin: 5px auto;
}

.dialog-footer {
  margin-top: 20px;
  text-align: center;
}
</style>
