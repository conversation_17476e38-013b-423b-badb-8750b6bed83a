<template>
    <el-dialog v-model="generateDialogVisible" width="910" top="90px">
        <template #header>
            <div class="header">
                <span>生成问题</span>
            </div>
        </template>
        <div class="middle">
            <InlineEditor v-model="prompt" style="width: 100%;" :height='630' :showToolbar="true" :placeholder="'请输入提示词'">
            </InlineEditor>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="handleConfirm" style="width: 120px; height: 35px;">
                    提交
                </el-button>
                <el-button @click="handleClose" style="width: 120px; height: 35px;">取消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { useDialogStore } from '@/stores/dialog';
import { ref, watch } from 'vue';
import InlineEditor from "@/components/editors/VditorInline.vue";
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { emitter } from '@/utils/emitter';
import { Event } from "@/types/event"
import { generateQuestion } from '@/apis/path/prjdetail';

const dialogStore = useDialogStore()
const generateDialogVisible = ref(dialogStore.generateDialogVisible)

const route = useRoute()
const uniqueCode = route.query.uniqueCode as string
const sectionId = ref('')
const belongProjectType = ref(0)

watch(
    () => dialogStore.generateDialogVisible,
    (newVal: boolean) => {
        if (newVal) {
            sectionId.value = dialogStore.sectionId
            belongProjectType.value = dialogStore.belongProjectType
        }
        generateDialogVisible.value = newVal;
    },
    { immediate: true } // 立即执行一次以初始化值
);

watch(
    generateDialogVisible,
    (newVal) => {
        if (newVal !== dialogStore.generateDialogVisible) {
            dialogStore.generateDialogVisible = newVal;
        }
    }
);

const prompt = ref('')

const handleClose = () => {
    prompt.value = ''
    generateDialogVisible.value = false;
    dialogStore.sectionId = ''
    dialogStore.belongProjectType = 0
};

const handleConfirm = async () => {
    const res: any = await generateQuestion(uniqueCode, belongProjectType.value, prompt.value, sectionId.value)
    if (res.success) {
        emitter.emit(Event.GENERATE)
        generateDialogVisible.value = false;
        dialogStore.sectionId = ''
        dialogStore.belongProjectType = 0
        ElMessage.success('开始生成问题')
    }
};
</script>

<style scoped lang="less">
.header {
    width: 880px;
    font-size: 16px;
    font-weight: 700;
    color: #333333;
    padding-bottom: 10px;
    border-bottom: 1px solid #dcdfe6;
}

.middle {
    width: 830px;
    height: 630px;
    margin: 5px auto;
}

.dialog-footer {
    margin-top: 20px;
    text-align: center;
}
</style>