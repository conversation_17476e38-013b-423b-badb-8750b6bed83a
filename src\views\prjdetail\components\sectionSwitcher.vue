<script setup lang="ts">
import { ref, watch } from 'vue';
import type { simpleSectionInfo } from '@/utils/type';
import { ArrowLeftBold, ArrowRightBold } from '@element-plus/icons-vue';

const emit = defineEmits(['changeSection']);
const props = defineProps({
  curSectionId: {
    type: Number,
    required: true
  },
  sectionList: {
    type: Array as () => simpleSectionInfo[],
    required: true
  }
});
const wrapperRef = ref();
const secList = ref<simpleSectionInfo[]>([]);
const curSecId = ref();
// const curSection = ref<simpleSectionInfo>();
watch(
  () => props,
  (newValue) => {
    secList.value = newValue.sectionList;
    curSecId.value = newValue.curSectionId;
  },
  { deep: true, immediate: true }
);
const handleWheel = (e: WheelEvent) => {
  const scrollTime = e.deltaY / 100; // 1次
  const speed = 130; // 1条 + margin
  wrapperRef.value.scrollLeft += scrollTime * speed;
};
const handleChangeSection = (newSectionId: number, shouldScroll: boolean = false) => {
  // 如果需要滚动，执行智能滚动逻辑
  if (shouldScroll) {
    const targetIndex = secList.value.findIndex(
      (sec: simpleSectionInfo) => sec.sectionId === newSectionId
    );
    const currentIndex = getCurrentSectionIndex();

    if (targetIndex !== -1) {
      const direction = targetIndex > currentIndex ? 'next' : 'prev';
      scrollToSection(targetIndex, direction);
    }
  }

  emit('changeSection', newSectionId);
};

// 获取当前章节在列表中的索引
const getCurrentSectionIndex = () => {
  return secList.value.findIndex((sec: simpleSectionInfo) => sec.sectionId === curSecId.value);
};

// 智能滚动到指定章节
const scrollToSection = (targetIndex: number, direction: 'prev' | 'next') => {
  if (!wrapperRef.value) return;

  const sectionWidth = 265; // 260px width + 5px margin
  const visibleSections = 4; // 可视区域显示的章节数量
  let targetScrollLeft = wrapperRef.value.scrollLeft; // 默认不滚动

  if (direction === 'next' && targetIndex >= 3) {
    // 从第4个章节开始，向右滚动
    if (targetIndex === secList.value.length - 1) {
      // 如果是最后一个章节，确保它完全可见
      targetScrollLeft = Math.max(0, (secList.value.length - visibleSections) * sectionWidth);
    } else {
      // 不是最后一个章节，正常向右滚动一个章节宽度
      targetScrollLeft = (targetIndex - 2) * sectionWidth; // 让目标章节显示在第3个位置
    }
  } else if (direction === 'prev') {
    // 向前切换时的滚动逻辑
    if (targetIndex <= 2) {
      // 如果切换到前3个章节（索引0,1,2），直接滚动到最左边
      targetScrollLeft = 0;
    } else {
      // 对于第4节及以后的向左切换，让目标章节显示在第2个位置
      // 这样可以确保向左切换时有一致的滚动行为
      targetScrollLeft = Math.max(0, (targetIndex - 1) * sectionWidth);
    }
  }

  // 平滑滚动到目标位置
  if (targetScrollLeft !== wrapperRef.value.scrollLeft) {
    wrapperRef.value.scrollTo({
      left: targetScrollLeft,
      behavior: 'smooth'
    });
  }
};

// 切换到上一章节
const handlePrevSection = () => {
  const currentIndex = getCurrentSectionIndex();
  if (currentIndex > 0) {
    const prevSection = secList.value[currentIndex - 1];
    scrollToSection(currentIndex - 1, 'prev');
    handleChangeSection(prevSection.sectionId);
  }
};

// 切换到下一章节
const handleNextSection = () => {
  const currentIndex = getCurrentSectionIndex();
  if (currentIndex < secList.value.length - 1) {
    const nextSection = secList.value[currentIndex + 1];
    scrollToSection(currentIndex + 1, 'next');
    handleChangeSection(nextSection.sectionId);
  }
};

// 判断是否可以切换到上一章节
const canGoPrev = () => {
  const currentIndex = getCurrentSectionIndex();
  return currentIndex > 0;
};

// 判断是否可以切换到下一章节
const canGoNext = () => {
  const currentIndex = getCurrentSectionIndex();
  return currentIndex < secList.value.length - 1;
};
</script>

<template>
  <div class="outer-wrapper">
    <!-- 左切换按钮 -->
    <span
      class="nav-button left-button"
      :class="{ disabled: !canGoPrev() }"
      @click="handlePrevSection"
    >
      <el-icon>
        <ArrowLeftBold />
      </el-icon>
    </span>

    <div class="switch-wrapper" ref="wrapperRef" @wheel.prevent="handleWheel">
      <span
        :title="sec.sectionName"
        @click="handleChangeSection(sec.sectionId, true)"
        class="section ellipsis-text"
        :class="curSecId == sec.sectionId ? 'isFocuse' : ''"
        v-for="(sec, idx) in secList"
        :key="sec.sectionId"
      >
        <span v-html="`第${idx + 1}节 ${sec.sectionName}`"></span>
      </span>
    </div>

    <!-- 右切换按钮 -->
    <span
      class="nav-button right-button"
      :class="{ disabled: !canGoNext() }"
      @click="handleNextSection"
    >
      <el-icon>
        <ArrowRightBold />
      </el-icon>
    </span>
  </div>
</template>

<style scoped>
.outer-wrapper {
  display: flex;
  align-items: center;
  flex-direction: row;
  margin-bottom: 20px;
  width: 100%;
  gap: 12px;

  .nav-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--color-primary);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;

    &:hover:not(.disabled) {
      background-color: var(--color-primary-dark, #409eff);
      transform: scale(1.1);
    }

    &.disabled {
      background-color: #c0c4cc;
      cursor: not-allowed;
      opacity: 0.6;
    }

    .el-icon {
      font-size: 16px;
    }
  }

  .icon {
    /*position: sticky;*/
    /*position: fixed;*/
    position: absolute;
    z-index: 10;
    background-color: var(--color-primary-transparent);
    display: flex;
    height: 20px;
    width: 20px;
    border-radius: 10px;
    justify-content: center;
    align-items: center;
    color: white;
  }

  .switch-wrapper {
    display: flex;
    flex-direction: row;
    width: 100%;
    overflow-x: hidden;

    .section {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 5px;
      font-size: 14px;
      border: 1px solid var(--color-primary);
      color: var(--color-primary);
      width: 260px;
      flex-shrink: 0;
      height: 35px;
      display: flex;
      align-items: center;
      padding: 0 10px;
      &:hover {
        cursor: pointer;
      }
      &.isFocuse {
        background-color: var(--color-primary);
        cursor: pointer;
        color: white;
      }
    }
  }
}
</style>
